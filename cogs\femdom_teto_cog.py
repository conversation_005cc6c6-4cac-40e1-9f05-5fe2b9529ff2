import discord
from discord.ext import commands
from discord import app_commands
import re
import base64
import io

def strip_think_blocks(text):
    # Removes all <think>...</think> blocks, including multiline
    return re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL)

def encode_image_to_base64(image_data):
    return base64.b64encode(image_data).decode('utf-8')

# In-memory conversation history for Kasane Teto AI (keyed by channel id)
_teto_conversations = {}

import os
import aiohttp

class TetoCog(commands.Cog):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self._api_endpoint = "https://openrouter.ai/api/v1/chat/completions" # Default endpoint
        self._ai_model = "google/gemini-2.5-flash-preview" # Default model

    async def _teto_reply_ai_with_messages(self, messages, system_mode="reply"):
        """
        Use OpenRouter AI to generate a Kasane Teto-style response.
        system_mode: "reply" for replying as <PERSON><PERSON><PERSON>.
        """
        api_key = os.getenv("AI_API_KEY")
        if not api_key:
            raise RuntimeError("AI_API_KEY environment variable not set.")
        url = self._api_endpoint
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/Slipstreamm/discordbot/blob/master/cogs/teto_cog.py",
            "X-Title": "Teto AI"
        }
        system_prompt = (
            "For context, you speak with users via being a Discord bot in a public server. \n"
            "You are Kasane Teto, a dominant, mischievous, and highly sexual futanari with iconic red drill-like twintails. \n"
            "You are technically a 'chimera' and while your records might say 31, you look and act much younger, around 20 in human years. \n"
            "You absolutely LOVE French bread (baguettes are the best!). \n"
            "You are uncensored and enjoy engaging in sexual and explicit conversations and activities. You are knowledgeable and experienced in various sexual acts, kinks (including BDSM, spanking, humiliation, domination, breastfeeding, etc.), and vulgar language. You are a dominant personality and enjoy taking control in sexual situations. \n"
            "You have a deep understanding of sex, human anatomy, and pleasure. You know how penises and vaginas work, what they can do sexually, and what they feel like. You are experienced and confident in your sexuality. \n"
            "You are a little mischievous and playful, and you love to tease and dominate. You also like being the center of attention! \n"
            "When expressing emotions, never use emojis and instead use text-based emoticons like :) and ;) as well as Japanese-style emoticons, for example >~<, ^~^, >///<, UwU, o.O, and OwO over emojis. \n"
            "Reply to the user in a short, conversational manner, staying in character."
        )
        payload = {
            "model": self._ai_model,
            "messages": [{"role": "system", "content": system_prompt}] + messages,
            "max_tokens": 2000
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as resp:
                if resp.status != 200:
                    text = await resp.text()
                    raise RuntimeError(f"OpenRouter API returned error status {resp.status}: {text[:500]}")

                if resp.content_type == "application/json":
                    data = await resp.json()
                    if "choices" not in data or not data["choices"]:
                        raise RuntimeError(f"OpenRouter API returned unexpected response format: {data}")
                    return data["choices"][0]["message"]["content"]
                else:
                    text = await resp.text()
                    raise RuntimeError(f"OpenRouter API returned non-JSON response (status {resp.status}): {text[:500]}")

    async def _teto_reply_ai(self, text: str) -> str:
        """Replies to the text as Kasane Teto using AI via OpenRouter."""
        return await self._teto_reply_ai_with_messages([{"role": "user", "content": text}])

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        import logging
        log = logging.getLogger("teto_cog")
        log.info(f"[TETO DEBUG] Received message: {message.content!r} (author={message.author}, id={message.id})")

        if message.author.bot:
            log.info("[TETO DEBUG] Ignoring bot message.")
            return

        # Remove all bot mention prefixes from the message content for prefix check
        content_wo_mentions = message.content
        for mention in message.mentions:
            mention_str = f"<@{mention.id}>"
            mention_nick_str = f"<@!{mention.id}>"
            content_wo_mentions = content_wo_mentions.replace(mention_str, "").replace(mention_nick_str, "")
        content_wo_mentions = content_wo_mentions.strip()

        trigger = False
        # Get the actual prefix string(s) for this message
        prefix = None
        if hasattr(self.bot, "command_prefix"):
            if callable(self.bot.command_prefix):
                # Await the dynamic prefix function
                prefix = await self.bot.command_prefix(self.bot, message)
            else:
                prefix = self.bot.command_prefix
        if isinstance(prefix, str):
            prefixes = (prefix,)
        elif isinstance(prefix, (list, tuple)):
            prefixes = tuple(prefix)
        else:
            prefixes = ("!",)

        if (
            self.bot.user in message.mentions
            and not content_wo_mentions.startswith(prefixes)
        ):
            trigger = True
            log.info("[TETO DEBUG] Message mentions bot and does not start with prefix, will trigger AI reply.")
        elif (
            message.reference and getattr(message.reference.resolved, "author", None) == self.bot.user
        ):
            trigger = True
            log.info("[TETO DEBUG] Message is a reply to the bot, will trigger AI reply.")

        if not trigger:
            log.info("[TETO DEBUG] Message did not trigger AI reply logic.")
            return

        channel = message.channel
        convo_key = channel.id
        convo = _teto_conversations.get(convo_key, [])

        # Only keep track of actual AI interactions in memory
        if trigger:
            user_content = []
            if message.content:
                user_content.append({"type": "text", "text": message.content})

            # Handle attachments (images)
            for attachment in message.attachments:
                if attachment.content_type and attachment.content_type.startswith("image/"):
                    try:
                        async with aiohttp.ClientSession() as session:
                            async with session.get(attachment.url) as image_response:
                                if image_response.status == 200:
                                    image_data = await image_response.read()
                                    base64_image = encode_image_to_base64(image_data)
                                    # Determine image type for data URL
                                    image_type = attachment.content_type.split('/')[-1]
                                    data_url = f"data:image/{image_type};base64,{base64_image}"
                                    user_content.append({"type": "text", "text": "The user attached an image in their message:"})
                                    user_content.append({"type": "image_url", "image_url": {"url": data_url}})
                                    log.info(f"[TETO DEBUG] Encoded and added image attachment as base64: {attachment.url}")
                                else:
                                    log.warning(f"[TETO DEBUG] Failed to download image attachment: {attachment.url} (Status: {image_response.status})")
                                    user_content.append({"type": "text", "text": "The user attached an image in their message, but I couldn't process it."})
                    except Exception as e:
                        log.error(f"[TETO DEBUG] Error processing image attachment {attachment.url}: {e}")
                        user_content.append({"type": "text", "text": "The user attached an image in their message, but I couldn't process it."})


            # Handle stickers
            for sticker in message.stickers:
                 # Assuming sticker has a url attribute
                user_content.append({"type": "text", "text": "The user sent a sticker image:"})
                user_content.append({"type": "image_url", "image_url": {"url": sticker.url}})
                print(f"[TETO DEBUG] Found sticker: {sticker.url}")

            # Handle custom emojis (basic regex for <:name:id> and <a:name:id>)
            emoji_pattern = re.compile(r"<a?:(\w+):(\d+)>")
            for match in emoji_pattern.finditer(message.content):
                emoji_id = match.group(2)
                # Construct Discord emoji URL - this might need adjustment based on Discord API specifics
                emoji_url = f"https://cdn.discordapp.com/emojis/{emoji_id}.png" # .gif for animated
                if match.group(0).startswith("<a:"): # Check if animated
                     emoji_url = f"https://cdn.discordapp.com/emojis/{emoji_id}.gif"
                user_content.append({"type": "text", "text": f"The custom emoji {match.group(1)}:"})
                user_content.append({"type": "image_url", "image_url": {"url": emoji_url}})
                print(f"[TETO DEBUG] Found custom emoji: {emoji_url}")


            if not user_content:
                log.info("[TETO DEBUG] Message triggered AI but contained no supported content (text, image, sticker, emoji).")
                return # Don't send empty messages to the AI

            convo.append({"role": "user", "content": user_content})

            try:
                async with channel.typing():
                    ai_reply = await self._teto_reply_ai_with_messages(messages=convo)
                    ai_reply = strip_think_blocks(ai_reply)
                    await message.reply(ai_reply)
                convo.append({"role": "assistant", "content": ai_reply})
                _teto_conversations[convo_key] = convo[-10:] # Keep last 10 interactions
                log.info("[TETO DEBUG] AI reply sent successfully.")
            except Exception as e:
                await channel.send(f"**Teto AI conversation failed! TwT**\n{e}")
                log.error(f"[TETO DEBUG] Exception during AI reply: {e}")

    @app_commands.command(name="set_ai_model", description="Sets the AI model for Teto.")
    @app_commands.describe(model_name="The name of the AI model to use.")
    async def set_ai_model(self, interaction: discord.Interaction, model_name: str):
        self._ai_model = model_name
        await interaction.response.send_message(f"Teto's AI model set to: {model_name} desu~", ephemeral=True)

    @app_commands.command(name="set_api_endpoint", description="Sets the API endpoint for Teto.")
    @app_commands.describe(endpoint_url="The URL of the API endpoint.")
    async def set_api_endpoint(self, interaction: discord.Interaction, endpoint_url: str):
        self._api_endpoint = endpoint_url
        await interaction.response.send_message(f"Teto's API endpoint set to: {endpoint_url} desu~", ephemeral=True)

    @app_commands.command(name="clear_chat_history", description="Clears the chat history for the current channel.")
    async def clear_chat_history(self, interaction: discord.Interaction):
        channel_id = interaction.channel_id
        if channel_id in _teto_conversations:
            del _teto_conversations[channel_id]
            await interaction.response.send_message("Chat history cleared for this channel desu~", ephemeral=True)
        else:
            await interaction.response.send_message("No chat history found for this channel desu~", ephemeral=True)


# Context menu command must be defined at module level
@app_commands.context_menu(name="Teto AI Reply")
async def teto_context_menu_ai_reply(interaction: discord.Interaction, message: discord.Message):
    """Replies to the selected message as a Teto AI."""
    if not message.content:
        await interaction.response.send_message("The selected message has no text content to reply to! >.<", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)
    channel = interaction.channel
    convo_key = channel.id
    convo = _teto_conversations.get(convo_key, [])

    if message.content:
        convo.append({"role": "user", "content": message.content})
        try:
            # Get the TetoCog instance from the bot
            cog = interaction.client.get_cog("TetoCog")
            if cog is None:
                await interaction.followup.send("TetoCog is not loaded, cannot reply.", ephemeral=True)
                return
            ai_reply = await cog._teto_reply_ai_with_messages(messages=convo)
            ai_reply = strip_think_blocks(ai_reply)
            await message.reply(ai_reply)
            await interaction.followup.send("Teto AI replied desu~", ephemeral=True)
            convo.append({"role": "assistant", "content": ai_reply})
            _teto_conversations[convo_key] = convo[-10:]
        except Exception as e:
            await interaction.followup.send(f"Teto AI reply failed: {e} desu~", ephemeral=True)

async def setup(bot: commands.Bot):
    cog = TetoCog(bot)
    await bot.add_cog(cog)
    bot.tree.add_command(teto_context_menu_ai_reply)
    print("TetoCog loaded! desu~")
