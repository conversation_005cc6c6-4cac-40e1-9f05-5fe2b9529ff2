import discord
from discord.ext import commands
from discord import app_commands
import random

class MessageCog(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        # Hardcoded message with {target} placeholder
        self.message_template = """
        {target} - Your pants are slowly and deliberately removed, leaving you feeling exposed and vulnerable. The sensation is both thrilling and terrifying as a presence looms over you, the only sound being the faint rustling of fabric as your clothes are discarded.
        """

    # Helper method for the message logic
    async def _message_logic(self, target):
        """Core logic for the message command."""
        # Replace {target} with the mentioned user
        return self.message_template.format(target=target)

    # --- RP Group ---
    rp = app_commands.Group(name="rp", description="Roleplay commands")
    
    @rp.command(name="molest", description="Send a hardcoded message to the mentioned user")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def molest_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of message."""
        response = await self._message_logic(member.mention)
        await interaction.response.send_message(response)

    @commands.command(name="molest")
    async def molest_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of molest."""
        response = await self._message_logic(member.mention)
        await ctx.reply(response)

    @rp.command(name="rape", description="Sends a message stating the author raped the mentioned user.")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to mention in the message")
    async def rape_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of rape."""
        rape_messages = [
            f"{interaction.user.mention} raped {member.mention}.",
            f"{interaction.user.mention} brutally raped {member.mention}.",
            f"{interaction.user.mention} unconsensually came inside {member.mention}.",
            f"{interaction.user.mention} traumatized {member.mention} via sexual assault.",
            f"{interaction.user.mention} dominated {member.mention} against their will.",
            f"{interaction.user.mention} took advantage of {member.mention}'s vulnerability.",
            f"{member.mention} suffered a brutal sexual violation at the hands of {interaction.user.mention}."
        ]
        response = random.choice(rape_messages)
        await interaction.response.send_message(response)

    @commands.command(name="rape")
    async def rape_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of rape."""
        rape_messages = [
            f"{ctx.author.mention} raped {member.mention}.",
            f"{ctx.author.mention} brutally raped {member.mention}.",
            f"{ctx.author.mention} unconsensually came inside {member.mention}.",
            f"{ctx.author.mention} forced themselves onto {member.mention}.",
            f"{ctx.author.mention} violated {member.mention} in a grotesque manner.",
            f"{member.mention} was unconsensually defiled by {ctx.author.mention}.",
            f"{ctx.author.mention} left {member.mention} traumatized after the assault."
        ]
        response = random.choice(rape_messages)
        await ctx.reply(response)

    @rp.command(name="sex", description="Send a normal sex message to the mentioned user")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def sex_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of sex."""
        sex_messages = [
            f"{interaction.user.mention} and {member.mention} shared a tender kiss that deepened into a passionate embrace.",
            f"{interaction.user.mention} gently caressed {member.mention}'s cheek before their lips met, igniting a spark.",
            f"With a soft touch, {interaction.user.mention} guided {member.mention}'s hand to their waist, pulling them closer.",
            f"{interaction.user.mention} whispered sweet nothings into {member.mention}'s ear, sending shivers down their spine.",
            f"Their bodies pressed together, {interaction.user.mention} and {member.mention} moved in a slow, sensual rhythm.",
            f"{member.mention} moaned softly as {interaction.user.mention}'s touch became more intimate.",
            f"{interaction.user.mention}'s fingers traced the curve of {member.mention}'s back, eliciting a gasp.",
            f"In the dim light, {interaction.user.mention} admired the beauty of {member.mention}'s form.",
            f"Their breaths mingled as {interaction.user.mention} and {member.mention} lost themselves in the moment.",
            f"{member.mention}'s legs wrapped around {interaction.user.mention}'s waist, pulling them into a deeper connection.",
            f"{interaction.user.mention} buried their face in {member.mention}'s neck, inhaling their scent.",
            f"The room filled with soft sounds of pleasure as {interaction.user.mention} and {member.mention} explored each other.",
            f"{member.mention}'s fingers tangled in {interaction.user.mention}'s hair, holding them close.",
            f"{interaction.user.mention}'s hips moved against {member.mention}'s, building a delicious tension.",
            f"With a final, shared sigh, {interaction.user.mention} and {member.mention} found release in each other's arms.",
            f"{interaction.user.mention} and {member.mention} lay tangled in the sheets, their bodies still humming with the afterglow.",
            f"{member.mention} rested their head on {interaction.user.mention}'s chest, listening to their heartbeat.",
            f"{interaction.user.mention} kissed {member.mention}'s forehead, a silent promise of more to come.",
            f"The scent of their lovemaking hung in the air as {interaction.user.mention} and {member.mention} drifted off to sleep.",
            f"{interaction.user.mention} and {member.mention} woke up intertwined, the morning sun casting a warm glow on their bodies.",
            f"{interaction.user.mention} and {member.mention} had a passionate night together.",
            f"{interaction.user.mention} made love to {member.mention}.",
            f"{member.mention} was pleasured by {interaction.user.mention}.",
            f"{interaction.user.mention} and {member.mention} shared an intimate moment.",
            f"{interaction.user.mention} and {member.mention} explored their desires.",
            f"{member.mention} felt a deep connection with {interaction.user.mention} during their encounter.",
            f"{interaction.user.mention} and {member.mention} experienced mutual pleasure."
        ]
        response = random.choice(sex_messages)
        await interaction.response.send_message(response)

    @commands.command(name="sex")
    async def sex_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of sex."""
        sex_messages = [
            f"{ctx.author.mention} and {member.mention} shared a tender kiss that deepened into a passionate embrace.",
            f"{ctx.author.mention} gently caressed {member.mention}'s cheek before their lips met, igniting a spark.",
            f"With a soft touch, {ctx.author.mention} guided {member.mention}'s hand to their waist, pulling them closer.",
            f"{ctx.author.mention} whispered sweet nothings into {member.mention}'s ear, sending shivers down their spine.",
            f"Their bodies pressed together, {ctx.author.mention} and {member.mention} moved in a slow, sensual rhythm.",
            f"{member.mention} moaned softly as {ctx.author.mention}'s touch became more intimate.",
            f"{ctx.author.mention}'s fingers traced the curve of {member.mention}'s back, eliciting a gasp.",
            f"In the dim light, {ctx.author.mention} admired the beauty of {member.mention}'s form.",
            f"Their breaths mingled as {ctx.author.mention} and {member.mention} lost themselves in the moment.",
            f"{member.mention}'s legs wrapped around {ctx.author.mention}'s waist, pulling them into a deeper connection.",
            f"{ctx.author.mention} buried their face in {member.mention}'s neck, inhaling their scent.",
            f"The room filled with soft sounds of pleasure as {ctx.author.mention} and {member.mention} explored each other.",
            f"{member.mention}'s fingers tangled in {ctx.author.mention}'s hair, holding them close.",
            f"{ctx.author.mention}'s hips moved against {member.mention}'s, building a delicious tension.",
            f"With a final, shared sigh, {ctx.author.mention} and {member.mention} found release in each other's arms.",
            f"{ctx.author.mention} and {member.mention} lay tangled in the sheets, their bodies still humming with the afterglow.",
            f"{member.mention} rested their head on {ctx.author.mention}'s chest, listening to their heartbeat.",
            f"{ctx.author.mention} kissed {member.mention}'s forehead, a silent promise of more to come.",
            f"The scent of their lovemaking hung in the air as {ctx.author.mention} and {member.mention} drifted off to sleep.",
            f"{ctx.author.mention} and {member.mention} woke up intertwined, the morning sun casting a warm glow on their bodies.",
            f"{ctx.author.mention} and {member.mention} had a passionate night together.",
            f"{ctx.author.mention} made love to {member.mention}.",
            f"{member.mention} was pleasured by {ctx.author.mention}.",
            f"{ctx.author.mention} and {member.mention} shared an intimate moment.",
            f"{ctx.author.mention} and {member.mention} explored their desires.",
            f"{member.mention} felt a deep connection with {ctx.author.mention} during their encounter.",
            f"{ctx.author.mention} and {member.mention} experienced mutual pleasure."
        ]
        response = random.choice(sex_messages)
        await ctx.reply(response)

    # --- Memes Group ---
    memes = app_commands.Group(name="memes", description="Meme and copypasta commands")

    @memes.command(name="seals", description="What the fuck did you just fucking say about me, you little bitch?")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def seals_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message("What the fuck did you just fucking say about me, you little bitch? I'll have you know I graduated top of my class in the Navy Seals, and I've been involved in numerous secret raids on Al-Quaeda, and I have over 300 confirmed kills. I am trained in gorilla warfare and I'm the top sniper in the entire US armed forces. You are nothing to me but just another target. I will wipe you the fuck out with precision the likes of which has never been seen before on this Earth, mark my fucking words. You think you can get away with saying that shit to me over the Internet? Think again, fucker. As we speak I am contacting my secret network of spies across the USA and your IP is being traced right now so you better prepare for the storm, maggot. The storm that wipes out the pathetic little thing you call your life. You're fucking dead, kid. I can be anywhere, anytime, and I can kill you in over seven hundred ways, and that's just with my bare hands. Not only am I extensively trained in unarmed combat, but I have access to the entire arsenal of the United States Marine Corps and I will use it to its full extent to wipe your miserable ass off the face of the continent, you little shit. If only you could have known what unholy retribution your little \"clever\" comment was about to bring down upon you, maybe you would have held your fucking tongue. But you couldn't, you didn't, and now you're paying the price, you goddamn idiot. I will shit fury all over you and you will drown in it. You're fucking dead, kiddo.")

    @commands.command(name="seals", help="What the fuck did you just fucking say about me, you little bitch?") # Assuming you want to keep this check for the legacy command
    async def seals_legacy(self, ctx):
        await ctx.send("What the fuck did you just fucking say about me, you little bitch? I'll have you know I graduated top of my class in the Navy Seals, and I've been involved in numerous secret raids on Al-Quaeda, and I have over 300 confirmed kills. I am trained in gorilla warfare and I'm the top sniper in the entire US armed forces. You are nothing to me but just another target. I will wipe you the fuck out with precision the likes of which has never been seen before on this Earth, mark my fucking words. You think you can get away with saying that shit to me over the Internet? Think again, fucker. As we speak I am contacting my secret network of spies across the USA and your IP is being traced right now so you better prepare for the storm, maggot. The storm that wipes out the pathetic little thing you call your life. You're fucking dead, kid. I can be anywhere, anytime, and I can kill you in over seven hundred ways, and that's just with my bare hands. Not only am I extensively trained in unarmed combat, but I have access to the entire arsenal of the United States Marine Corps and I will use it to its full extent to wipe your miserable ass off the face of the continent, you little shit. If only you could have known what unholy retribution your little \"clever\" comment was about to bring down upon you, maybe you would have held your fucking tongue. But you couldn't, you didn't, and now you're paying the price, you goddamn idiot. I will shit fury all over you and you will drown in it. You're fucking dead, kiddo.")

    @memes.command(name="notlikeus", description="Honestly i think They Not Like Us is the only mumble rap song that is good")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def notlikeus_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message("Honestly i think They Not Like Us is the only mumble rap song that is good, because it calls out Drake for being a Diddy blud")

    @commands.command(name="notlikeus", help="Honestly i think They Not Like Us is the only mumble rap song that is good") # Assuming you want to keep this check for the legacy command
    async def notlikeus_legacy(self, ctx):
        await ctx.send("Honestly i think They Not Like Us is the only mumble rap song that is good, because it calls out Drake for being a Diddy blud")

    @memes.command(name="pmo", description="icl u pmo")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def pmo_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message("icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt")

    @commands.command(name="pmo", help="icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt")
    async def pmo_legacy(self, ctx: commands.Context):
        await ctx.send("icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt")

async def setup(bot: commands.Bot):
    await bot.add_cog(MessageCog(bot))
