<!-- Custom Bot Section -->
<div id="custom-bot-section" class="dashboard-section" style="display: none;">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Custom Bot</h2>
            <p class="text-sm text-gray-600">Run your own personalized Discord bot with your own token, profile picture, and username.</p>
        </div>
    </div>

    <!-- Bot Status Card -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Bot Status</h3>
        </div>
        <div class="card-body">
            <div id="bot-status-indicator" class="flex items-center mb-4">
                <div id="status-dot" class="w-4 h-4 rounded-full bg-gray-400 mr-2"></div>
                <span id="status-text">Checking status...</span>
            </div>
            <div id="bot-controls" class="flex gap-2">
                <button id="start-bot-button" class="btn btn-primary" disabled>Start Bot</button>
                <button id="stop-bot-button" class="btn btn-danger" disabled>Stop Bot</button>
            </div>
            <div id="bot-error" class="mt-4 text-red-500 hidden"></div>
        </div>
    </div>

    <!-- Bot Configuration Card -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Bot Configuration</h3>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label for="bot-token-input">Bot Token:</label>
                <input type="password" id="bot-token-input" class="w-full" placeholder="Enter your Discord bot token">
                <p class="text-sm text-gray-600 mt-1">Your bot token is stored securely and never shared.</p>
            </div>
            <div class="form-group">
                <label for="bot-prefix-input">Command Prefix:</label>
                <input type="text" id="bot-prefix-input" class="w-full" placeholder="!" maxlength="5">
                <p class="text-sm text-gray-600 mt-1">The prefix users will type before commands (e.g., !help).</p>
            </div>
            <div class="form-group">
                <label for="bot-status-type-select">Status Type:</label>
                <select id="bot-status-type-select" class="w-full">
                    <option value="playing">Playing</option>
                    <option value="listening">Listening to</option>
                    <option value="watching">Watching</option>
                    <option value="competing">Competing in</option>
                </select>
            </div>
            <div class="form-group">
                <label for="bot-status-text-input">Status Text:</label>
                <input type="text" id="bot-status-text-input" class="w-full" placeholder="!help" maxlength="128">
                <p class="text-sm text-gray-600 mt-1">The text that will appear in your bot's status.</p>
            </div>
            <div class="btn-group">
                <button id="save-bot-config-button" class="btn btn-primary">Save Configuration</button>
            </div>
        </div>
    </div>

    <!-- Bot Creation Guide Card -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">How to Create Your Bot</h3>
        </div>
        <div class="card-body">
            <ol class="list-decimal pl-5 space-y-4">
                <li>
                    <strong>Create a Discord Application:</strong>
                    <p>Go to the <a href="https://discord.com/developers/applications" target="_blank" class="text-blue-500 hover:underline">Discord Developer Portal</a> and click "New Application".</p>
                </li>
                <li>
                    <strong>Set Up Your Bot:</strong>
                    <p>In your application, go to the "Bot" tab and click "Add Bot".</p>
                </li>
                <li>
                    <strong>Customize Your Bot:</strong>
                    <p>Upload a profile picture and set a username for your bot.</p>
                </li>
                <li>
                    <strong>Get Your Bot Token:</strong>
                    <p>Click "Reset Token" to generate a new token, then copy it.</p>
                    <p class="text-red-500">IMPORTANT: Never share your bot token with anyone!</p>
                </li>
                <li>
                    <strong>Set Bot Permissions:</strong>
                    <p>In the "Bot" tab, under "Privileged Gateway Intents", enable:</p>
                    <ul class="list-disc pl-5">
                        <li>Presence Intent</li>
                        <li>Server Members Intent</li>
                        <li>Message Content Intent</li>
                    </ul>
                </li>
                <li>
                    <strong>Invite Your Bot:</strong>
                    <p>Go to the "OAuth2" tab, then "URL Generator". Select the following scopes:</p>
                    <ul class="list-disc pl-5">
                        <li>bot</li>
                        <li>applications.commands</li>
                    </ul>
                    <p>Then select the following bot permissions:</p>
                    <ul class="list-disc pl-5">
                        <li>Send Messages</li>
                        <li>Embed Links</li>
                        <li>Attach Files</li>
                        <li>Read Message History</li>
                        <li>Use Slash Commands</li>
                        <li>Add Reactions</li>
                    </ul>
                    <p>Copy the generated URL and open it in your browser to invite the bot to your server.</p>
                </li>
                <li>
                    <strong>Configure Your Bot:</strong>
                    <p>Paste your bot token in the configuration form above and save it.</p>
                </li>
                <li>
                    <strong>Start Your Bot:</strong>
                    <p>Click the "Start Bot" button to bring your bot online!</p>
                </li>
            </ol>
        </div>
    </div>
</div>
