import discord
from discord.ext import commands
from discord import app_commands
import random

class MessageCog(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    # --- RP Group ---
    rp = app_commands.Group(name="rp", description="Roleplay commands")

    @rp.command(name="sex", description="Send a normal sex message to the mentioned user")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def sex_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of sex."""
        sex_messages = [
            f"{interaction.user.mention} roughly pins {member.mention} against the wall, their lips crashing together in a desperate, hungry kiss.",
            f"{interaction.user.mention}'s hands roam possessively over {member.mention}'s body, leaving a trail of heat in their wake.",
            f"A low moan escapes {member.mention}'s throat as {interaction.user.mention} finds a particularly sensitive spot.",
            f"Their bodies grind together, the friction building an unbearable tension between {interaction.user.mention} and {member.mention}.",
            f"{interaction.user.mention} whispers filthy promises in {member.mention}'s ear, making them tremble with anticipation.",
            f"{member.mention}'s nails dig into {interaction.user.mention}'s back as they surrender to the overwhelming pleasure.",
            f"The air is thick with the scent of sweat and arousal as {interaction.user.mention} and {member.mention} lose themselves in the act.",
            f"{interaction.user.mention}'s thrusts become deeper, more insistent, driving {member.mention} to the brink.",
            f"A shared cry of release echoes in the room as {interaction.user.mention} and {member.mention} climax together.",
            f"Panting and sated, {interaction.user.mention} collapses onto {member.mention}, their bodies slick with sweat.",
            f"{member.mention}'s legs are wrapped tightly around {interaction.user.mention}'s waist, unwilling to let go.",
            f"{interaction.user.mention} buries their face in {member.mention}'s neck, leaving a hot, wet kiss.",
            f"The sheets are tangled around {interaction.user.mention} and {member.mention}'s legs, a testament to their passionate encounter.",
            f"{member.mention} traces the lines of {interaction.user.mention}'s muscles, admiring the power in their body.",
            f"{interaction.user.mention} kisses {member.mention}'s lips again, a possessive, lingering kiss.",
            f"The room is filled with the sounds of their heavy breathing as {interaction.user.mention} and {member.mention} slowly recover.",
            f"{member.mention} moans softly as {interaction.user.mention} begins to stir, hinting at another round.",
            f"{interaction.user.mention} grins wickedly at {member.mention}, knowing exactly what they want.",
            f"Their bodies are still connected, unwilling to break the intimate bond they've created.",
            f"{interaction.user.mention} and {member.mention} are lost in their own world, the rest of the world forgotten.",
            f"{interaction.user.mention} and {member.mention} had a wild and passionate night together.",
            f"{interaction.user.mention} pleasured {member.mention} intensely.",
            f"{member.mention} was thoroughly dominated by {interaction.user.mention}.",
            f"{interaction.user.mention} and {member.mention} explored their deepest desires without inhibition.",
            f"{member.mention} felt an intense connection with {interaction.user.mention} during their explicit encounter.",
            f"{interaction.user.mention} and {member.mention} experienced explosive mutual pleasure."
        ]
        response = random.choice(sex_messages)
        await interaction.response.send_message(response)

    @commands.command(name="sex")
    async def sex_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of sex."""
        sex_messages = [
            f"{ctx.author.mention} roughly pins {member.mention} against the wall, their lips crashing together in a desperate, hungry kiss.",
            f"{ctx.author.mention}'s hands roam possessively over {member.mention}'s body, leaving a trail of heat in their wake.",
            f"A low moan escapes {member.mention}'s throat as {ctx.author.mention} finds a particularly sensitive spot.",
            f"Their bodies grind together, the friction building an unbearable tension between {ctx.author.mention} and {member.mention}.",
            f"{ctx.author.mention} whispers filthy promises in {member.mention}'s ear, making them tremble with anticipation.",
            f"{member.mention}'s nails dig into {ctx.author.mention}'s back as they surrender to the overwhelming pleasure.",
            f"The air is thick with the scent of sweat and arousal as {ctx.author.mention} and {member.mention} lose themselves in the act.",
            f"{ctx.author.mention}'s thrusts become deeper, more insistent, driving {member.mention} to the brink.",
            f"A shared cry of release echoes in the room as {ctx.author.mention} and {member.mention} climax together.",
            f"Panting and sated, {ctx.author.mention} collapses onto {member.mention}, their bodies slick with sweat.",
            f"{member.mention}'s legs are wrapped tightly around {ctx.author.mention}'s waist, unwilling to let go.",
            f"{ctx.author.mention} buries their face in {member.mention}'s neck, leaving a hot, wet kiss.",
            f"The sheets are tangled around {ctx.author.mention} and {member.mention}'s legs, a testament to their passionate encounter.",
            f"{member.mention} traces the lines of {ctx.author.mention}'s muscles, admiring the power in their body.",
            f"{ctx.author.mention} kisses {member.mention}'s lips again, a possessive, lingering kiss.",
            f"The room is filled with the sounds of their heavy breathing as {ctx.author.mention} and {member.mention} slowly recover.",
            f"{member.mention} moans softly as {ctx.author.mention} begins to stir, hinting at another round.",
            f"{ctx.author.mention} grins wickedly at {member.mention}, knowing exactly what they want.",
            f"Their bodies are still connected, unwilling to break the intimate bond they've created.",
            f"{ctx.author.mention} and {member.mention} are lost in their own world, the rest of the world forgotten.",
            f"{ctx.author.mention} and {member.mention} had a wild and passionate night together.",
            f"{ctx.author.mention} pleasured {member.mention} intensely.",
            f"{member.mention} was thoroughly dominated by {ctx.author.mention}.",
            f"{ctx.author.mention} and {member.mention} explored their deepest desires without inhibition.",
            f"{member.mention} felt an intense connection with {ctx.author.mention} during their explicit encounter.",
            f"{ctx.author.mention} and {member.mention} experienced explosive mutual pleasure."
        ]
        response = random.choice(sex_messages)
        await ctx.reply(response)

    @rp.command(name="rape", description="Sends a message stating the author raped the mentioned user.")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to mention in the message")
    async def rape_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of rape."""
        rape_messages = [
            f"{interaction.user.mention} raped {member.mention}.",
            f"{interaction.user.mention} brutally raped {member.mention}.",
            f"{interaction.user.mention} unconsensually came inside {member.mention}.",
            f"{interaction.user.mention} traumatized {member.mention} via sexual assault.",
            f"{interaction.user.mention} dominated {member.mention} against their will.",
            f"{interaction.user.mention} took advantage of {member.mention}'s vulnerability.",
            f"{member.mention} suffered a brutal sexual violation at the hands of {interaction.user.mention}."
        ]
        response = random.choice(rape_messages)
        await interaction.response.send_message(response)

    @rp.command(name="kiss", description="Send a wholesome kiss message to the mentioned user")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def kiss_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of kiss."""
        kiss_messages = [
            f"{interaction.user.mention} gives {member.mention} a sweet kiss on the cheek.",
            f"{interaction.user.mention} leans in and gives {member.mention} a gentle kiss.",
            f"{interaction.user.mention} plants a soft kiss on {member.mention}'s forehead.",
            f"{interaction.user.mention} and {member.mention} share a quick, affectionate kiss.",
            f"{interaction.user.mention} gives {member.mention} a warm, lingering kiss.",
            f"{interaction.user.mention} kisses {member.mention}'s hand tenderly.",
            f"{interaction.user.mention} gives {member.mention} a playful peck on the nose.",
            f"{interaction.user.mention} and {member.mention} share a loving kiss.",
            f"{interaction.user.mention} gives {member.mention} a comforting kiss.",
            f"{interaction.user.mention} kisses {member.mention} with a smile.",
            f"{interaction.user.mention} gives {member.mention} a butterfly kiss with their eyelashes.",
            f"{interaction.user.mention} blows {member.mention} a sweet air kiss.",
            f"{interaction.user.mention} gives {member.mention} a tender kiss on the lips.",
            f"{interaction.user.mention} surprises {member.mention} with a quick kiss.",
            f"{interaction.user.mention} kisses {member.mention}'s fingertips delicately.",
            f"{interaction.user.mention} gives {member.mention} an eskimo kiss, rubbing noses.",
            f"{interaction.user.mention} plants a loving kiss on {member.mention}'s temple.",
            f"{interaction.user.mention} gives {member.mention} a passionate but gentle kiss.",
            f"{interaction.user.mention} kisses {member.mention} under the starlight.",
            f"{interaction.user.mention} gives {member.mention} a goodnight kiss.",
        ]
        response = random.choice(kiss_messages)
        await interaction.response.send_message(response)

    @commands.command(name="kiss")
    async def kiss_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of kiss."""
        kiss_messages = [
            f"{ctx.author.mention} gives {member.mention} a sweet kiss on the cheek.",
            f"{ctx.author.mention} leans in and gives {member.mention} a gentle kiss.",
            f"{ctx.author.mention} plants a soft kiss on {member.mention}'s forehead.",
            f"{ctx.author.mention} and {member.mention} share a quick, affectionate kiss.",
            f"{ctx.author.mention} gives {member.mention} a warm, lingering kiss.",
            f"{ctx.author.mention} kisses {member.mention}'s hand tenderly.",
            f"{ctx.author.mention} gives {member.mention} a playful peck on the nose.",
            f"{ctx.author.mention} and {member.mention} share a loving kiss.",
            f"{ctx.author.mention} gives {member.mention} a comforting kiss.",
            f"{ctx.author.mention} kisses {member.mention} with a smile.",
        ]
        response = random.choice(kiss_messages)
        await ctx.reply(response)

    @rp.command(name="hug", description="Send a wholesome hug message to the mentioned user")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def hug_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of hug."""
        hug_messages = [
            f"{interaction.user.mention} gives {member.mention} a warm hug.",
            f"{interaction.user.mention} wraps their arms around {member.mention} in a comforting hug.",
            f"{interaction.user.mention} and {member.mention} share a tight hug.",
            f"{interaction.user.mention} gives {member.mention} a gentle hug.",
            f"{interaction.user.mention} pulls {member.mention} into a loving embrace.",
            f"{interaction.user.mention} and {member.mention} share a long, heartfelt hug.",
            f"{interaction.user.mention} gives {member.mention} a friendly hug.",
            f"{interaction.user.mention} hugs {member.mention} tightly.",
            f"{interaction.user.mention} gives {member.mention} a warm, fuzzy hug.",
            f"{interaction.user.mention} and {member.mention} share a sweet hug.",
            f"{interaction.user.mention} gives {member.mention} a bear hug that lifts them off the ground.",
            f"{interaction.user.mention} wraps {member.mention} in a protective embrace.",
            f"{interaction.user.mention} gives {member.mention} a surprise hug from behind.",
            f"{interaction.user.mention} and {member.mention} share a cozy group hug.",
            f"{interaction.user.mention} gives {member.mention} a quick side hug.",
            f"{interaction.user.mention} embraces {member.mention} with open arms.",
            f"{interaction.user.mention} gives {member.mention} a reassuring hug.",
            f"{interaction.user.mention} squeezes {member.mention} in a playful hug.",
            f"{interaction.user.mention} gives {member.mention} a healing hug that makes everything better.",
            f"{interaction.user.mention} and {member.mention} share a moment in a tender embrace.",
        ]
        response = random.choice(hug_messages)
        await interaction.response.send_message(response)

    @commands.command(name="hug")
    async def hug_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of hug."""
        hug_messages = [
            f"{ctx.author.mention} gives {member.mention} a warm hug.",
            f"{ctx.author.mention} wraps their arms around {member.mention} in a comforting hug.",
            f"{ctx.author.mention} and {member.mention} share a tight hug.",
            f"{ctx.author.mention} gives {member.mention} a gentle hug.",
            f"{ctx.author.mention} pulls {member.mention} into a loving embrace.",
            f"{ctx.author.mention} and {member.mention} share a long, heartfelt hug.",
            f"{ctx.author.mention} gives {member.mention} a friendly hug.",
            f"{ctx.author.mention} hugs {member.mention} tightly.",
            f"{ctx.author.mention} gives {member.mention} a warm, fuzzy hug.",
            f"{ctx.author.mention} and {member.mention} share a sweet hug.",
        ]
        response = random.choice(hug_messages)
        await ctx.reply(response)

    # --- Memes Group ---
    memes = app_commands.Group(name="memes", description="Meme and copypasta commands")

    @memes.command(name="seals", description="What the fuck did you just fucking say about me, you little bitch?")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def seals_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message("What the fuck did you just fucking say about me, you little bitch? I'll have you know I graduated top of my class in the Navy Seals, and I've been involved in numerous secret raids on Al-Quaeda, and I have over 300 confirmed kills. I am trained in gorilla warfare and I'm the top sniper in the entire US armed forces. You are nothing to me but just another target. I will wipe you the fuck out with precision the likes of which has never been seen before on this Earth, mark my fucking words. You think you can get away with saying that shit to me over the Internet? Think again, fucker. As we speak I am contacting my secret network of spies across the USA and your IP is being traced right now so you better prepare for the storm, maggot. The storm that wipes out the pathetic little thing you call your life. You're fucking dead, kid. I can be anywhere, anytime, and I can kill you in over seven hundred ways, and that's just with my bare hands. Not only am I extensively trained in unarmed combat, but I have access to the entire arsenal of the United States Marine Corps and I will use it to its full extent to wipe your miserable ass off the face of the continent, you little shit. If only you could have known what unholy retribution your little \"clever\" comment was about to bring down upon you, maybe you would have held your fucking tongue. But you couldn't, you didn't, and now you're paying the price, you goddamn idiot. I will shit fury all over you and you will drown in it. You're fucking dead, kiddo.")

    @commands.command(name="seals", help="What the fuck did you just fucking say about me, you little bitch?") # Assuming you want to keep this check for the legacy command
    async def seals_legacy(self, ctx):
        await ctx.send("What the fuck did you just fucking say about me, you little bitch? I'll have you know I graduated top of my class in the Navy Seals, and I've been involved in numerous secret raids on Al-Quaeda, and I have over 300 confirmed kills. I am trained in gorilla warfare and I'm the top sniper in the entire US armed forces. You are nothing to me but just another target. I will wipe you the fuck out with precision the likes of which has never been seen before on this Earth, mark my fucking words. You think you can get away with saying that shit to me over the Internet? Think again, fucker. As we speak I am contacting my secret network of spies across the USA and your IP is being traced right now so you better prepare for the storm, maggot. The storm that wipes out the pathetic little thing you call your life. You're fucking dead, kid. I can be anywhere, anytime, and I can kill you in over seven hundred ways, and that's just with my bare hands. Not only am I extensively trained in unarmed combat, but I have access to the entire arsenal of the United States Marine Corps and I will use it to its full extent to wipe your miserable ass off the face of the continent, you little shit. If only you could have known what unholy retribution your little \"clever\" comment was about to bring down upon you, maybe you would have held your fucking tongue. But you couldn't, you didn't, and now you're paying the price, you goddamn idiot. I will shit fury all over you and you will drown in it. You're fucking dead, kiddo.")

    @memes.command(name="notlikeus", description="Honestly i think They Not Like Us is the only mumble rap song that is good")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def notlikeus_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message("Honestly i think They Not Like Us is the only mumble rap song that is good, because it calls out Drake for being a Diddy blud")

    @commands.command(name="notlikeus", help="Honestly i think They Not Like Us is the only mumble rap song that is good") # Assuming you want to keep this check for the legacy command
    async def notlikeus_legacy(self, ctx):
        await ctx.send("Honestly i think They Not Like Us is the only mumble rap song that is good, because it calls out Drake for being a Diddy blud")

    @memes.command(name="pmo", description="icl u pmo")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def pmo_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message("icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt")

    @commands.command(name="pmo", help="icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt")
    async def pmo_legacy(self, ctx: commands.Context):
        await ctx.send("icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt")

async def setup(bot: commands.Bot):
    await bot.add_cog(MessageCog(bot))
