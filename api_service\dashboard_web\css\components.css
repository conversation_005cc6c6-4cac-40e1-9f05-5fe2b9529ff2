/* Components CSS file for the Discord Bot Dashboard */

/* Loading Spinner */
.loading-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-6);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(88, 101, 242, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

.loading-spinner-sm {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner-lg {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Button with loading state */
.btn-loading {
  position: relative;
  pointer-events: none;
  color: transparent !important;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: calc(50% - 8px);
  left: calc(50% - 8px);
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.toast {
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-md);
  background-color: var(--card-bg);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  min-width: 300px;
  max-width: 450px;
  animation: slideIn 0.3s ease-out forwards;
}

.toast.toast-success {
  border-left: 4px solid var(--success-color);
}

.toast.toast-error {
  border-left: 4px solid var(--danger-color);
}

.toast.toast-warning {
  border-left: 4px solid var(--warning-color);
}

.toast.toast-info {
  border-left: 4px solid var(--primary-color);
}

.toast-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  margin-bottom: var(--spacing-1);
}

.toast-message {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.toast-close {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 1.25rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-close:hover {
  color: var(--text-primary);
}

.toast.toast-hiding {
  animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Badges */
.badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 10px;
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.badge-success {
  background-color: var(--success-color);
  color: white;
}

.badge-warning {
  background-color: var(--warning-color);
  color: white;
}

.badge-danger {
  background-color: var(--danger-color);
  color: white;
}

.badge-light {
  background-color: #e2e8f0;
  color: var(--text-primary);
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-6);
}

.tab {
  padding: var(--spacing-3) var(--spacing-4);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  color: var(--text-secondary);
  transition: all 0.2s;
}

.tab:hover {
  color: var(--primary-color);
}

.tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Modals */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s;
}

.modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 500px;
  animation: scaleIn 0.3s;
  position: relative;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.modal-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-6);
  overflow-y: auto;
}

.modal-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Dropdowns */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  display: none;
  min-width: 180px;
  padding: var(--spacing-2) 0;
  margin: var(--spacing-1) 0 0;
  background-color: var(--card-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
}

.dropdown-menu.show {
  display: block;
  animation: fadeIn 0.2s;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-4);
  clear: both;
  font-weight: 400;
  color: var(--text-primary);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  text-decoration: none;
}

.dropdown-divider {
  height: 0;
  margin: var(--spacing-2) 0;
  overflow: hidden;
  border-top: 1px solid var(--border-color);
}

/* Alerts */
.alert {
  position: relative;
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-4);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
}

.alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc;
}

.alert-danger {
  color: #842029;
  background-color: #f8d7da;
  border-color: #f5c2c7;
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

.alert-info {
  color: #055160;
  background-color: #cff4fc;
  border-color: #b6effb;
}

.alert-dismissible {
  padding-right: 3rem;
}

.alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  padding: var(--spacing-4);
  color: inherit;
  background: transparent;
  border: 0;
  cursor: pointer;
}

/* Progress Bar */
.progress {
  display: flex;
  height: 0.75rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: var(--radius-md);
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: var(--primary-color);
  transition: width 0.6s ease;
}

/* Tooltips */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: var(--secondary-color);
  color: #fff;
  text-align: center;
  border-radius: var(--radius-sm);
  padding: var(--spacing-2);
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.75rem;
}

.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--secondary-color) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Error message */
.error-message {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 4px solid var(--danger-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-3) var(--spacing-4);
  margin: var(--spacing-3) 0;
  color: var(--text-primary);
}

.error-message p {
  margin: var(--spacing-1) 0;
}

.error-message code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}
