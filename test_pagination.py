import asyncio
import logging
import sys
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("test_pagination")

# Add the current directory to the path so we can import the cogs
sys.path.append(os.getcwd())

from cogs.safebooru_cog import SafebooruCog
from discord.ext import commands
import discord

async def test_pagination():
    # Create a mock bot with intents
    intents = discord.Intents.default()
    bot = commands.Bot(command_prefix='!', intents=intents)

    # Initialize the cog
    cog = SafebooruCog(bot)

    # Test the pagination for a specific tag
    tag = "kasane_teto"
    log.info(f"Testing pagination for tag: {tag}")

    # Call the _fetch_posts_logic method
    results = await cog._fetch_posts_logic("test", tag)

    # Check the results
    if isinstance(results, tuple):
        log.info(f"Found {len(results[1])} results")
        # Print the first few results
        for i, result in enumerate(results[1][:5]):
            log.info(f"Result {i+1}: {result.get('id')} - {result.get('file_url')}")
    else:
        log.error(f"Error: {results}")

    # Clean up
    if hasattr(cog, 'session') and cog.session and not cog.session.closed:
        await cog.session.close()
        log.info("Closed aiohttp session")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_pagination())
